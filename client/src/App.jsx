import { useState } from "react";
import { BsCloudUpload } from "react-icons/bs";
import ReactMarkdown from "react-markdown";

const App = () => {
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState("");

  const handleFileUpload = () => {
    // Create a file input element
    const fileInput = document.createElement("input");
    // Set the file input attributes
    fileInput.type = "file";
    // Set the file input to accept only PDF files
    fileInput.accept = "application/pdf";
    fileInput.onchange = async () => {
      if (fileInput.files && fileInput.files.length > 0) {
        const file = fileInput.files.item(0);
        if (file) {
          const formData = new FormData();
          formData.append("pdf", file);

          // Send the file to the server
          console.log("Uploading file...");
          await fetch("http://localhost:8000/api/upload", {
            method: "POST",
            body: formData,
          });

          console.log("File uploaded successfully");
        }
      }
    };
    fileInput.click();
  };

  const handleSendChatMessage = async () => {
    setMessages((prev) => [...prev, { role: "user", content: message }]);
    setMessage("");
    const res = await fetch(`http://localhost:8000/chat?message=${message}`);
    const data = await res.json();
    setMessages((prev) => [
      ...prev,
      {
        role: "assistant",
        content: data?.message,
        documents: data?.docs,
      },
    ]);
  };

  return (
    <div className='flex w-full min-h-screen bg-base-200'>
      {/* Sidebar for PDF upload */}
      <div className='card bg-base-100 shadow-xl rounded-box flex flex-col items-center justify-center h-screen w-1/4 mr-4 p-6'>
        <div
          className='flex flex-col justify-center items-center gap-4 bg-primary px-6 py-4 rounded-lg cursor-pointer hover:bg-primary-focus transition-all duration-200 shadow-md'
          onClick={handleFileUpload}
        >
          <span className='text-white text-xl font-bold tracking-wide'>
            Upload Your PDF file
          </span>
          <BsCloudUpload size={40} color='white' />
        </div>
      </div>
      {/* Main chat area */}
      <div className='card bg-base-100 shadow-xl rounded-box flex flex-col h-screen w-3/4 p-0'>
        <div
          className='flex-1 overflow-y-auto p-10 space-y-6'
          style={{ maxHeight: "calc(100vh - 80px)" }}
        >
          {messages.map((message, index) => (
            <div
              key={index}
              className={`chat ${
                message.role === "user" ? "chat-start" : "chat-end"
              }`}
            >
              <div
                className={`chat-bubble ${
                  message.role === "user"
                    ? "chat-bubble-neutral text-base"
                    : "chat-bubble-info text-base"
                }`}
              >
                <ReactMarkdown>{message.content}</ReactMarkdown>
              </div>
            </div>
          ))}
        </div>
        {/* Chat input area */}
        <div className='w-full flex gap-3 p-6 bg-base-300 rounded-b-box'>
          <input
            type='text'
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleSendChatMessage();
              }
            }}
            className='input input-bordered w-full text-xl'
            placeholder='Type your message...'
          />
          <button className='btn btn-primary' onClick={handleSendChatMessage}>
            Send
          </button>
        </div>
      </div>
    </div>
  );
};

export default App;
