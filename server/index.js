import "dotenv/config";
import express from "express";
import cors from "cors";
import multer from "multer";
import { Worker } from "worker_threads";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";
import { GoogleGenAI } from "@google/genai";
import { vectorStore } from "./utils/embeddings.js";
import { Document } from "@langchain/core/documents";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

function runWorker(data) {
    return new Promise((resolve, reject) => {
        const worker = new Worker(path.resolve(__dirname, "pdfWorker.js"));
        worker.postMessage(data);
        worker.on("message", resolve);
        worker.on("error", reject);
        worker.on("exit", (code) => {
            if (code !== 0) reject(new Error(`Worker stopped with code ${code}`));
        });
    });
}

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, "uploads/");
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
        cb(null, uniqueSuffix + file.originalname);
    },
});

const upload = multer({ storage: storage });

const app = express();
app.use(cors());
const port = 8000;

app.get("/", (req, res) => {
    res.send("Hello World!");
});

app.post("/api/upload", upload.single("pdf"), async (req, res) => {
    const jobData = {
        filename: req.file.originalname,
        destination: req.file.destination,
        path: req.file.path,
    };

    try {
        const result = await runWorker(jobData);
        if (result.success) {
            res.json({ message: "PDF processed and stored successfully" });
        } else {
            res.status(500).json({ error: result.error });
        }
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

app.get("/chat", async (req, res) => {
    const userQuery = req.query.message;

    const ret = vectorStore.asRetriever();
    const result = await ret.invoke(Document.fromText(userQuery));

    const date = new Date();
    const currentDate = date.toDateString();

    const SYSTEM_PROMPT = `You are a helpful AI Assistant who answers the user query based on the available context from PDF File. 

    Context:
    ${JSON.stringify(result)}
    
    This the Current Date: ${currentDate}
    User Query: ${userQuery}

    Answer the user query based on the context provided. GIVE ANSWER IN MARKDOWN FORMAT ONLY.
    `;

    const ai = new GoogleGenAI({
        apiKey: process.env.GOOGLE_API_KEY,
    });

    const response = await ai.models.generateContent({
        model: "gemini-2.5-flash-lite-preview-06-17",
        contents: userQuery,
        config: {
            systemInstruction: SYSTEM_PROMPT,
        },
    });

    return res.json({
        message: response.text,
        docs: result,
    });
});

app.listen(port, () => {
    console.log(`Example app listening on port ${port}`);
});
