import "dotenv/config";
import { PGVectorStore } from "@langchain/community/vectorstores/pgvector";
import { GoogleGenerativeAIEmbeddings } from "@langchain/google-genai";
import { TaskType } from "@google/generative-ai";
import { Document } from "@langchain/core/documents";
import { RecursiveCharacterTextSplitter } from "@langchain/textsplitters";

const embeddings = new GoogleGenerativeAIEmbeddings({
    model: "text-embedding-004",
    taskType: TaskType.RETRIEVAL_DOCUMENT,
    apiKey: process.env.GOOGLE_API_KEY,
});

export const vectorStore = await PGVectorStore.initialize(embeddings, {
    postgresConnectionOptions: {
        type: "postgres",
        host: "langchain-vectore-store-rith-agent.b.aivencloud.com",
        port: 19095,
        user: "avnadmin",
        password: "AVNS_fF9cqfgC5m8z-u_V6wl",
        database: "defaultdb",
        ssl: {
            rejectUnauthorized: false,
            ca: process.env.ca,
        },
    },
    tableName: "chat_with_pdf",
    columns: {
        idColumnName: "id",
        vectorColumnName: "vector",
        contentColumnName: "content",
        metadataColumnName: "metadata",
    },
    distanceStrategy: "cosine",
});

export const addToVectoreStore = async (data) => {
    const { doc, metadata } = data;

    try {
        const docs = [
            new Document({
                pageContent: doc,
                metadata,
            }),
        ];

        // Split the video into chunks
        const splitter = new RecursiveCharacterTextSplitter({
            chunkSize: 1000,
            chunkOverlap: 200,
        });

        const chunks = await splitter.splitDocuments(docs);

        await vectorStore.addDocuments(chunks);
        console.log(`✅ All ${chunks.length} chunks added to vector store`);
        return true;
    } catch (error) {
        console.error("❌ Error adding to vector store:", error);
        return false;
    }
};
