{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "devDependencies": {"@types/express": "^5.0.3", "@types/multer": "^1.4.13", "@types/node": "^24.0.3", "nodemon": "^3.1.10"}, "dependencies": {"@google/genai": "^1.5.1", "@google/generative-ai": "^0.24.1", "@langchain/community": "^0.3.46", "@langchain/core": "^0.3.59", "@langchain/google-genai": "^0.2.13", "@langchain/mongodb": "^0.1.0", "@langchain/ollama": "^0.2.2", "@langchain/openai": "^0.5.14", "@langchain/qdrant": "^0.1.2", "@langchain/textsplitters": "^0.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mongodb": "^6.17.0", "multer": "^2.0.1", "pdf-parse": "^1.1.1", "pg": "^8.16.2", "uuid": "^11.1.0"}}