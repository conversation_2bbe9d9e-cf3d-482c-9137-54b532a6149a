hoistPattern:
  - '*'
hoistedDependencies:
  '@anthropic-ai/sdk@0.27.3':
    '@anthropic-ai/sdk': private
  '@browserbasehq/sdk@2.6.0':
    '@browserbasehq/sdk': private
  '@browserbasehq/stagehand@1.14.0(@playwright/test@1.53.1)(deepmerge@4.3.1)(dotenv@16.5.0)(openai@5.5.1(ws@8.18.2)(zod@3.25.67))(zod@3.25.67)':
    '@browserbasehq/stagehand': private
  '@cfworker/json-schema@4.1.1':
    '@cfworker/json-schema': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.11.0)':
    '@graphql-typed-document-node/core': private
  '@grpc/grpc-js@1.13.4':
    '@grpc/grpc-js': private
  '@grpc/proto-loader@0.7.15':
    '@grpc/proto-loader': private
  '@ibm-cloud/watsonx-ai@1.6.7':
    '@ibm-cloud/watsonx-ai': private
  '@js-sdsl/ordered-map@4.4.2':
    '@js-sdsl/ordered-map': private
  '@langchain/weaviate@0.2.0(@langchain/core@0.3.59(openai@5.5.1(ws@8.18.2)(zod@3.25.67)))':
    '@langchain/weaviate': private
  '@mongodb-js/saslprep@1.3.0':
    '@mongodb-js/saslprep': private
  '@playwright/test@1.53.1':
    '@playwright/test': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@qdrant/js-client-rest@1.14.1(typescript@5.8.3)':
    '@qdrant/js-client-rest': private
  '@qdrant/openapi-typescript-fetch@1.2.6':
    '@qdrant/openapi-typescript-fetch': private
  '@sevinf/maybe@0.5.0':
    '@sevinf/maybe': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/express-serve-static-core@5.0.6':
    '@types/express-serve-static-core': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/retry@0.12.0':
    '@types/retry': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  abort-controller-x@0.4.3:
    abort-controller-x: private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@2.0.0:
    accepts: private
  agent-base@7.1.3:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@5.2.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  append-field@1.0.0:
    append-field: private
  argparse@2.0.1:
    argparse: private
  asynckit@0.4.0:
    asynckit: private
  axios@1.10.0(debug@4.4.1):
    axios: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bignumber.js@9.3.0:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@2.2.0:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bson@6.10.4:
    bson: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  camelcase@6.3.0:
    camelcase: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  console-table-printer@2.14.3:
    console-table-printer: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cross-fetch@3.2.0:
    cross-fetch: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  decamelize@1.2.0:
    decamelize: private
  deepmerge@4.3.1:
    deepmerge: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dotenv@16.5.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  expr-eval@2.0.2:
    expr-eval: private
  extend@3.0.2:
    extend: private
  file-type@16.5.4:
    file-type: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  flat@5.0.2:
    flat: private
  follow-redirects@1.15.9(debug@4.4.1):
    follow-redirects: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.0:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gaxios@6.7.1:
    gaxios: private
  gcp-metadata@6.1.1:
    gcp-metadata: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  google-auth-library@9.15.1:
    google-auth-library: private
  google-logging-utils@0.0.2:
    google-logging-utils: private
  gopd@1.2.0:
    gopd: private
  graphql-request@6.1.0(graphql@16.11.0):
    graphql-request: private
  graphql@16.11.0:
    graphql: private
  gtoken@7.1.0:
    gtoken: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  humanize-ms@1.2.1:
    humanize-ms: private
  ibm-cloud-sdk-core@5.4.0:
    ibm-cloud-sdk-core: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-promise@4.0.0:
    is-promise: private
  is-stream@2.0.1:
    is-stream: private
  isstream@0.1.2:
    isstream: private
  js-tiktoken@1.0.20:
    js-tiktoken: private
  js-yaml@4.1.0:
    js-yaml: private
  json-bigint@1.0.0:
    json-bigint: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jwa@1.4.2:
    jwa: private
  jws@4.0.0:
    jws: private
  langchain@0.3.28(@langchain/core@0.3.59(openai@5.5.1(ws@8.18.2)(zod@3.25.67)))(@langchain/google-genai@0.2.13(@langchain/core@0.3.59(openai@5.5.1(ws@8.18.2)(zod@3.25.67))))(@langchain/ollama@0.2.2(@langchain/core@0.3.59(openai@5.5.1(ws@8.18.2)(zod@3.25.67))))(axios@1.10.0)(openai@5.5.1(ws@8.18.2)(zod@3.25.67))(ws@8.18.2):
    langchain: private
  langsmith@0.3.33(openai@5.5.1(ws@8.18.2)(zod@3.25.67)):
    langsmith: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.once@4.1.1:
    lodash.once: private
  long@5.3.2:
    long: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  memory-pager@1.5.0:
    memory-pager: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mkdirp@0.5.6:
    mkdirp: private
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: private
  ms@2.1.3:
    ms: private
  mustache@4.2.0:
    mustache: private
  negotiator@1.0.0:
    negotiator: private
  nice-grpc-client-middleware-retry@3.1.11:
    nice-grpc-client-middleware-retry: private
  nice-grpc-common@2.0.2:
    nice-grpc-common: private
  nice-grpc@2.1.12:
    nice-grpc: private
  node-domexception@1.0.0:
    node-domexception: private
  node-ensure@0.0.0:
    node-ensure: private
  node-fetch@2.7.0:
    node-fetch: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  ollama@0.5.16:
    ollama: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  openai@5.5.1(ws@8.18.2)(zod@3.25.67):
    openai: private
  openapi-types@12.1.3:
    openapi-types: private
  p-finally@1.0.0:
    p-finally: private
  p-queue@6.6.2:
    p-queue: private
  p-retry@4.6.2:
    p-retry: private
  p-timeout@3.2.0:
    p-timeout: private
  parseurl@1.3.3:
    parseurl: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  peek-readable@4.1.0:
    peek-readable: private
  pg-cloudflare@1.2.6:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.2):
    pg-pool: private
  pg-protocol@1.10.2:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  picomatch@2.3.1:
    picomatch: private
  playwright-core@1.53.1:
    playwright-core: private
  playwright@1.53.1:
    playwright: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  process@0.11.10:
    process: private
  protobufjs@7.5.3:
    protobufjs: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  pstree.remy@1.1.8:
    pstree.remy: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  querystringify@2.2.0:
    querystringify: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  readable-stream@3.6.2:
    readable-stream: private
  readable-web-to-node-stream@3.0.4:
    readable-web-to-node-stream: private
  readdirp@3.6.0:
    readdirp: private
  require-directory@2.1.1:
    require-directory: private
  requires-port@1.0.0:
    requires-port: private
  retry-axios@2.6.0(axios@1.10.0):
    retry-axios: private
  retry@0.13.1:
    retry: private
  router@2.2.0:
    router: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.2:
    semver: private
  send@1.2.0:
    send: private
  serve-static@2.2.0:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  simple-wcswidth@1.1.1:
    simple-wcswidth: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  split2@4.2.0:
    split2: private
  statuses@2.0.2:
    statuses: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strtok3@6.3.0:
    strtok3: private
  supports-color@5.5.0:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-types@4.2.1:
    token-types: private
  touch@3.1.1:
    touch: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  ts-error@1.0.6:
    ts-error: private
  type-is@2.0.1:
    type-is: private
  typedarray@0.0.6:
    typedarray: private
  typescript@5.8.3:
    typescript: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@7.8.0:
    undici-types: private
  undici@6.21.3:
    undici: private
  universalify@0.2.0:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  url-parse@1.5.10:
    url-parse: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vary@1.1.2:
    vary: private
  weaviate-client@3.6.2:
    weaviate-client: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-url@14.2.0:
    whatwg-url: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.2:
    ws: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  zod-to-json-schema@3.24.5(zod@3.25.67):
    zod-to-json-schema: private
  zod@3.25.67:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Thu, 19 Jun 2025 18:28:05 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.2
  - fsevents@2.3.3
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
